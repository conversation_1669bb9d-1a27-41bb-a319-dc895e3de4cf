#!/usr/bin/env python3
"""
종합 시나리오 테스트 스크립트
실제 사용자 시나리오를 기반으로 한 전체 시스템 테스트
"""
import os
import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# OpenAI API 키 설정
os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'

from core.agent_processor import ToolCallingAgentProcessor

def main():
    print('🧪 종합 시나리오 테스트')
    print('=' * 60)

    agent = ToolCallingAgentProcessor()

    test_scenarios = [
        ('일반 문의', '안녕하세요! 무선 이어폰 배터리 수명이 궁금해요'),
        ('주문 조회', '제 주문 상태를 확인하고 싶어요. 주문번호는 ORD20241201001입니다'),
        ('배송 문의', '배송비는 얼마나 되나요?'),
        ('반품 문의', '상품 반품은 어떻게 하나요?'),
        ('복합 문의', '주문번호 ORD20241201001의 배송 상태와 예상 도착일을 알려주세요')
    ]

    for scenario_name, query in test_scenarios:
        print(f'\n📋 {scenario_name}: {query}')
        print('-' * 50)
        try:
            result = agent.process_query(query, user_id=1, session_id='comprehensive_test')
            response_preview = result["response"][:150] + "..." if len(result["response"]) > 150 else result["response"]
            print(f'✅ 응답: {response_preview}')
            if result.get('tools_used'):
                print(f'🔧 사용된 도구: {result["tools_used"]}')
            print(f'⏱️ 응답 시간: {result["response_time"]:.2f}초')
        except Exception as e:
            print(f'❌ 오류: {e}')
        print()

    print('✅ 종합 시나리오 테스트 완료!')

if __name__ == "__main__":
    main()
