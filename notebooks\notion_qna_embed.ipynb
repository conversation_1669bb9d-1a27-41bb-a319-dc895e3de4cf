{"cells": [{"cell_type": "markdown", "id": "bf82caf6", "metadata": {}, "source": ["# 📘 Notion Markdown QA 임베딩 파이프라인\n", "이 노트북은 Notion에서 Export한 Markdown 파일들을 병합하고, 각 문단으로부터 질문-답변(QA) 쌍을 생성하여 Chroma 벡터 DB에 저장합니다."]}, {"cell_type": "code", "execution_count": 4, "id": "149bd060", "metadata": {}, "outputs": [], "source": ["import os\n", "from langchain.schema import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.prompts import PromptTemplate\n", "from langchain.chains import LLMChain\n", "from langchain.vectorstores import Chroma\n", "from langchain.embeddings import OpenAIEmbeddings"]}, {"cell_type": "markdown", "id": "cfdf7cce", "metadata": {}, "source": ["## 1️⃣ Markdown 병합 함수"]}, {"cell_type": "code", "execution_count": 5, "id": "69ec7247", "metadata": {}, "outputs": [], "source": ["def merge_markdown_files(base_dir: str) -> str:\n", "    merged = \"\"\n", "    for root, _, files in os.walk(base_dir):\n", "        for file in files:\n", "            if file.endswith(\".md\"):\n", "                path = os.path.join(root, file)\n", "                with open(path, \"r\", encoding=\"utf-8\") as f:\n", "                    content = f.read()\n", "                    merged += f\"\\n\\n# {file}\\n{content}\"\n", "    return merged"]}, {"cell_type": "markdown", "id": "d1f23dfe", "metadata": {}, "source": ["## 2️⃣ 텍스트 분할 함수"]}, {"cell_type": "code", "execution_count": 33, "id": "a7a5b7f9", "metadata": {}, "outputs": [], "source": ["def split_by_title(text: str):\n", "    chunks = text.split(\"\\n# \")\n", "    cleaned = [\"# \" + c.strip() if not c.startswith(\"#\") else c.strip() for c in chunks if c.strip()]\n", "    return cleaned\n"]}, {"cell_type": "markdown", "id": "45181f9f", "metadata": {}, "source": ["## 3️⃣ 질문 생성 체인 함수 (답변 → 예상 질문)"]}, {"cell_type": "code", "execution_count": null, "id": "6957f6e8", "metadata": {}, "outputs": [], "source": ["def build_qa_chain():\n", "    prompt = PromptTemplate(\n", "        input_variables=[\"answer\"],\n", "        template=\"\"\"\n", "아래는 고객에게 제공된 안내문입니다. 고객이 이 내용을 받기 전에 어떤 질문을 했을지 유추하여 \"Q:\"로 시작하는 질문과 \"A:\"로 시작하는 답변을 구성하세요.\n", "\n", "답변:\n", "{answer}\n", "\n", "형식:\n", "Q: (예상 질문)\n", "A: (원본 답변)\n", "\"\"\"\n", "    )\n", "    llm = ChatOpenAI(model_name=\"gpt-4\", temperature=0.3)\n", "    return LLMChain(prompt=prompt, llm=llm)\n"]}, {"cell_type": "code", "execution_count": null, "id": "7f49d670", "metadata": {}, "outputs": [], "source": ["\n", "def build_qa_chain_json():\n", "    prompt = PromptTemplate(\n", "        input_variables=[\"answer\"],\n", "        template=\"\"\"\n", "아래는 고객에게 제공된 안내문입니다. 고객이 이 내용을 받기 전에 어떤 질문을 했을지 유추하여 JSON 형식으로 반환하세요.\n", "\n", "반환 형식:\n", "{{\n", "  \"faq_id\": \"FAQ###\",       // 고유 ID는 FAQ001부터 자동 증가하거나 생략 가능\n", "  \"category\": \"\",           // 적절한 카테고리를 추정해서 넣어주세요 (예: 배송, 교환, 설치 등)\n", "  \"question\": \"\",           // 유추된 질문\n", "  \"answer\": \"\",             // 원본 답변\n", "  \"keywords\": \"\"            // 질문/답변에 포함된 핵심 키워드 (쉼표로 구분)\n", "}}\n", "\n", "답변:\n", "{answer}\n", "\"\"\"\n", "    )\n", "    llm = ChatOpenAI(model_name=\"gpt-4\", temperature=0.3)\n", "    return LLMChain(prompt=prompt, llm=llm)\n"]}, {"cell_type": "markdown", "id": "a16bc650", "metadata": {}, "source": ["## 4️⃣ QA Document 생성 함수"]}, {"cell_type": "code", "execution_count": 8, "id": "b4aaab60", "metadata": {}, "outputs": [], "source": ["def generate_documents(chunks, chain):\n", "    documents = []\n", "    for chunk in chunks:\n", "        qa = chain.run(answer=chunk)\n", "        documents.append(Document(page_content=qa, metadata={}))\n", "    return documents"]}, {"cell_type": "markdown", "id": "b6e68567", "metadata": {}, "source": ["## 5️⃣ Chroma에 저장 함수"]}, {"cell_type": "code", "execution_count": 9, "id": "39af303a", "metadata": {}, "outputs": [], "source": ["def store_to_chroma(documents, persist_dir=\"./chroma_notion_qa\"):\n", "    embeddings = OpenAIEmbeddings()\n", "    db = Chroma.from_documents(documents, embeddings, persist_directory=persist_dir)\n", "    db.persist()\n", "    return db"]}, {"cell_type": "markdown", "id": "eaba9691", "metadata": {}, "source": ["## ✅ 실행: 병합 → 분할 → QA 생성 → 저장"]}, {"cell_type": "code", "execution_count": 27, "id": "2d0a5034", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 탐색 경로: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\(구매 후 바로) 양품, 검수해서 보내주세요 21f032cbb6d481a0bfd8e10d91cbe58e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\7179 내부 사이즈 21f032cbb6d48159b4e7e747bf595f29.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\LT2 업데이트 파일 21f032cbb6d481ffb2d2c0d29b71cd34.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\LT2 업데이트 해도 안됩니다 21f032cbb6d4810b9b61e36581e8167c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\X6 어플 업데이트 21f032cbb6d481ee9b0ad444f1151f54.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\X6 어플 업데이트 해도 안됩니다 21f032cbb6d48186b513e1d4e3cd90c8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\X6 전원 어댑터 스펙 21f032cbb6d4817894d9db3c790cf850.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\검수문제없음) 반송 반품배송비 청구 21f032cbb6d481dc840fcaf866bc93a3.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\계좌입금하도록 하겠습니다 21f032cbb6d481fd8ed0d364563dd9f5.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\고객귀책으로 잘못 선택해서 일부만 환불 ᄇ 21f032cbb6d481c587e9da8cfb1651a8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\고스트 투명 아크릴 거실 사이드테이블 소파 보조 21f032cbb6d481779cd8f0d34a472084.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\국내 부품 발송해드려도 될까요 21f032cbb6d4819cad30ec2a1d45b949.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\국내, 교환 재고 부족 21f032cbb6d4814dabcdfb703a3682e7.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\국내배송, 언제 배송되나요 → 예정일을 모를  21f032cbb6d481aebf77dc02edd033bf.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\국내배송, 언제 배송되나요 → 예정일을 알때 21f032cbb6d4812eaa51d8f0ca89972a.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\국내부품발송 송장전달 21f032cbb6d481498d13e76a1f2b9781.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\단순변심) 단순변심반품비 얼마예요 21f032cbb6d481d29203ee3cd1b0edde.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\단순변심) 반품수거하도록 하겠습니 21f032cbb6d481898f73d9d93abcaea9.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\단순변심) 배송지연인데 왜 단순변심인가 21f032cbb6d481c293d6d3c193af7150.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\단순변심) 해외 제품 반품비용 왜 이렇게 비 21f032cbb6d4814b92e8e1478e0b0d5e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\단순변심) 해외왕복비 금액 청구 기준, 사ᄉ 21f032cbb6d481f2b4cbee1874d006b5.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\더더 내부 사이즈 21f032cbb6d481c1b20be9b370d9c59c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\마켓에 반품접수해주세요 (송장이 입력되어이 21f032cbb6d481ed9db6d47f9ba78d98.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\마켓에 취소 접수 안하고 취소 가능한지 여부만 21f032cbb6d481238861c6846e8a56fa.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\마켓에 클레임 접수가 안되어 있어 환불처리  21f032cbb6d48143a7a0c456d71d5c62.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\마켓에는 배송완료라고 떴지만 아직 배송이 ᄋ 21f032cbb6d481f9af47c5fdf895394c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\무타공 충전식 LED 터치 붙이는 침실 자석 ᄇ 21f032cbb6d481f7b9c4f1964946e077.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\뭉게구름 칼블럭 빠짐 현상 21f032cbb6d4819286cfc71871c40507.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\미세한 칠까짐 흠집 스크래치 21f032cbb6d481f894e2f914adcc20f3.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\박스 버렸을때 다른 박스에 포장 21f032cbb6d481b29979eb120b4115f8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\박스 훼손 등 실사용 문제없는 경우 클레이 21f032cbb6d4812abf11ca542cad0c21.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품 검수 중 21f032cbb6d481cab471d457f41581f5.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품 수거 되었습니다 21f032cbb6d4814c862dc8a132bcb336.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품 창고에 입고 된 내역이 없습니다 21f032cbb6d4815ca9d3f25f3ae68ffe.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품 확인 완료, 교환 진행됨 21f032cbb6d4813fbf36f40d4f7718c6.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품검수) 반품 검수 하겠습니다 21f032cbb6d4815d8ff0dd66fa29cfda.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품교환) 상품하자 명확할 때 21f032cbb6d4816f91fbe943b4745747.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품수거지연 반품수거재접수 21f032cbb6d4815b9f16d1669bbfb718.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품접수 전(#) 환불 or 교환 확인 필요 21f032cbb6d481bc8c74c2d3e9467aa9.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품접수 후(#) 환불 or 교환 확인 필요 21f032cbb6d48199b0e4cdf6f184e443.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\반품환불) 상품하자 명확할 때 21f032cbb6d481358416c2fe43edf32f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 간이통관 = 결재통보 21f032cbb6d48175bd1ffb094056375b.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 간이통관 = 결재통보 문자 못 받았다 21f032cbb6d48139b8cce545b1435820.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 간이통관 = 관부가세 납부했는데 수리ᄀ 21f032cbb6d48189b4d2ee58d05ee0d9.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 간이통관 = 관부가세 영수증 주세요 21f032cbb6d481a8a933fa57eeb198d4.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 간이통관 = 수입신고수리뜸 21f032cbb6d481b8b3c7ec07b9462d66.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 경동화물로 이관됨 21f032cbb6d481bd9ac7d0793d39f733.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 드림 결제대기 결제완료 발송대기 → 포ᄌ 21f032cbb6d48174895de1372f34d912.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 드림 발송완료 → 포장완료, 선적완료 21f032cbb6d48186af05fc44411dc2de.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 멀티송장, 분할출고, 분리배송 21f032cbb6d48106b15be68ddf453c8f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 반출완료 21f032cbb6d4813a812ae9ae9faa982b.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 배송완료 21f032cbb6d4811bb694d92b9cfd8164.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 배송중 21f032cbb6d481e8a063d88e945b0f62.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 선송장→배송중이라고 오해하는 경우 21f032cbb6d481bd98aec33bd8d14b18.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 입항&하선 21f032cbb6d4816d9592c935c16cc7e2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 포장 무게측정, 재고 언제 도착하는지 ᄆ 21f032cbb6d481bb9a8dfcec2d0a2900.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 포장 무게측정, 재고 언제 도착할지 알 21f032cbb6d481e2b4c3e4c4afe04bc5.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 해외, 검수 안함 21f032cbb6d481669cedc5dd2ba5a7de.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 해외, 배송소요일 안내 왜 다르죠 21f032cbb6d481edb702e08ff9dc133e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송) 해외, 지금 사면 언제와요 21f032cbb6d4815d961ad9b670f64186.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송지정일 설정 방법 21f032cbb6d4817fb317c5e10c2d8eb3.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송지정일 설정 불가 해외 불가능 21f032cbb6d481958f56f2eb5626084e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\배송지정일날 발송하겠습니다 21f032cbb6d481fbb185d1e557a6812d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\변경 완료 21f032cbb6d481d08173e351424036bb.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부분환불 21f032cbb6d481878374c1528dcd293a.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부품누락) 손잡이, 나사 등 작은 부품 박스  21f032cbb6d48161ada9e0ae95e8c178.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부품발송) 국내수입 상품이지만 부품이 국ᄂ 21f032cbb6d48198855be8a01baa57a6.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부품발송) 국내에서 부품발송 21f032cbb6d4818f8122df6b6c3b4b4d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부품발송) 부품 구매 가능 21f032cbb6d48193918bf5b866e56ee8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부품발송) 부품 구매 불가 21f032cbb6d4812eb665caf2f54205d8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\부품발송) 해외에서 부품발송 21f032cbb6d4812db749ef427dbb0be7.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\북유럽 럭셔리 푹신한 카페 라운지체어 윙체ᄋ 21f032cbb6d4814690b8de1880e1dfe7.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\상품) 해외, 상세페이지 기재된 내용 외의 추가  21f032cbb6d4810683dfdaa552809947.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\수령 후 30일 초과인 경우 클레임불가 21f032cbb6d481c5bbe5e67269dccced.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\슬림 내부 사이즈 21f032cbb6d481728ac3dbc894b44c6d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\시계 작동 안됨 클레임 영상 요청 21f032cbb6d481de835cf2418752cd30.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\어제 출고 배송되었습니다 21f032cbb6d481a79966cd4365aa85dd.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\오늘 금일 출고 배송 되었습니다 21f032cbb6d481bb9b35d5b394b31574.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\옵션 변경 가능 (재고부족) 21f032cbb6d481e8a0b0c4fbc405a88b.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\옵션 변경 불가 (가격차이→ 차액입금) 21f032cbb6d481098b58c3dec0ccf194.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\옵션 변경 불가 (재구매 취소접수O ) 21f032cbb6d48131baf6d26a57c425a2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\옵션 변경 불가 (재구매 취소접수X ) 21f032cbb6d48128be4ac3f366d75deb.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\옵션 변경 불가 (품절상품) 21f032cbb6d48107b205d569f3de22ae.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\옵션에 없는 상품 판매 하시나요 재입고예정 21f032cbb6d481b7ab1bcf1029491dd8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\요청 주신 배송지정일이 너무 늦을 때 21f032cbb6d48187b8eae05a60744983.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로 피렌체 거치대 파손 클레임처리불가 21f032cbb6d481378b07c49b31639152.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로 피렌체 거치대 파손 해외부품 발송 21f032cbb6d4819ea853f94097736176.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로 피렌체 와이어 길이가 짧아서 조립이 안된 21f032cbb6d4818e9778dc78d3c82d5d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로 피렌체 장스탠드 지지대 구입 안내 21f032cbb6d481c59688cb3ef0682e79.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로 피렌체 전등갓 각도가 조절 되지 않아요 21f032cbb6d481c89ff6ee68df8efaab.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로 피렌체 조립 어떻게 해요 21f032cbb6d481fcaf29d4d8c36b596e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\유로피렌체 관련 모든 영상 링크 21f032cbb6d48162a55adbacfa6ef595.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\이미 오전 9시 반에 출고 되어 취소불가 21f032cbb6d481c497e5d44ea3fa0673.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\이미 출고하여 변경 불가 21f032cbb6d481db95e8c6c89eaa2665.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\일부 누락 재배송 or 부분환불 중에 어느 거 21f032cbb6d481e48867c59afc2b59ff.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\재배송 안내 21f032cbb6d481bdad61ef8b67bb2a67.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\제목 없음 21f032cbb6d481c8ac7dc447c213a4c2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\조립 및 해체 가능 21f032cbb6d481b5ad8ddd77b205a12e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\조명 시공 필요한 제품입니다 21f032cbb6d481b5a6fcf440f6843032.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\주문번호 확인이 안되니 주문번호 회신 주세ᄋ 21f032cbb6d48115bae5dd7b30e52468.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\착불로 받았어요, 착불비 환급 21f032cbb6d481048b51fb1818547cc9.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\철제 제품 조립 유격 문제 21f032cbb6d4818a8c9ecad4dbc65dac.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\취소불가능 21f032cbb6d481e6a649eac294322235.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\취소완료 21f032cbb6d48197ba8ece5a0136abed.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\츄비 빛이 약해요 전원이 안켜져 21f032cbb6d481239bd9d49b1604fa3d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\츄비 충전이 안됩니다 21f032cbb6d481eea3d5f6740e73e948.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\클레임 사진 요청 21f032cbb6d481c1ac6fdd7ce86247ac.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\클레임 영상 요청 21f032cbb6d48130951af45f3fa77763.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\택배분실 재배송 or 환불 중에 어느 거 21f032cbb6d4814d88dbecccea51ec21.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\통관) 사업자통관하고싶어요 21f032cbb6d48123872cd693ab4f1220.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\통관) 통관부호 문자 왔어요 이거 뭐예요 21f032cbb6d481e2b82ac5b436cb22e8.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\통관) 통관부호 불일치 미기재 21f032cbb6d4819dbfdde4441939d7a6.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\통관) 통관부호 전화번호만 불일치 21f032cbb6d481e88b7ef48bd00c4887.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\통관) 통관부호가 뭐예요 통관부호 발급방버 21f032cbb6d48113b2e6e7c33c810a49.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\통관) 통관부호일치 21f032cbb6d48139ae10fe1a481f39e0.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\폐기 비용 지원 안내 21f032cbb6d4813abc3ad2e04ff93aae.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\폐기교환 (자체폐기가능상품) 21f032cbb6d481958976d84ffade4a3f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\폐기교환 (폐기비용지원) 21f032cbb6d48136afd0f8a3e62e76eb.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\폐기비용 실비 차액 환급 21f032cbb6d4810aad26cfafbbe258eb.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\폐기환불 (자체폐기가능상품) 21f032cbb6d481e6b21af9e52747f998.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\폐기환불 (폐기비용지원) 21f032cbb6d48158af62ea34e5220477.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\품절건 취소해주세요 21f032cbb6d48184a123cff22234ceac.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\품절문자 못받았다고 강성 항의 21f032cbb6d481539c8ada10cc19a16c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\프르타벨레 이동식 브리튼 매거진 랙 사이드테ᄋ 21f032cbb6d4818a8153c69ee83589af.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외 부품 발송해드려도 될까요 21f032cbb6d48133b0d9fab6c1f70970.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외 상품 KC인증 등의 관련 문의할때 21f032cbb6d481d998f2ecd60d4a487e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외) 배송지정을 꼭 해줘야 할때 알림사항 21f032cbb6d4810682abdf598edc615c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외) 이미 출고했는데 주문취소 혹은 배송지ᄋ 21f032cbb6d481d29323d59b59fc9706.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외, 가격 차이가 나서 취소 원해요 21f032cbb6d4816f9431da5be99fa14c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외, 가격이 달라졌어요 21f032cbb6d481dd9da8f8ac26ab17aa.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외, 교환 재고 부족 21f032cbb6d48167b6d8d0816562bf82.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외부품발송 송장전달 21f032cbb6d481eda9f7ef3b72512268.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\해외제품 as 불가 21f032cbb6d481a685f1f427d70e9ca7.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) AS 안내 21f032cbb6d48133a31ad192db12f55e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 가격이 왜 비싸졌나요 21f032cbb6d481f4974bd7d448afed37.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 가격이 왜 저렴하죠 21f032cbb6d481d7857ee53513ce3748.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 네이버 스토어팜 착불비 발생 원이 21f032cbb6d4810d8b2ecd12593d5258.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 등판 분해 조립 21f032cbb6d4818c948ad6b7bfb786fb.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 럼버서포트 분해 조립 21f032cbb6d4810789a2e260b6ea7aaa.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 메쉬 간격 21f032cbb6d481129cb1c09975cc9fbf.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 메쉬 뜯어짐 - 프레임 연결 마감 ᄇ 21f032cbb6d481ce82f7c91d24ec702d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 문제O 회수 없이 부품 발송 21f032cbb6d48103a355f293d8ddee01.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 미네랄 A사이즈 단종 21f032cbb6d481e29bc3dd57f398efbb.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 미네랄 가격이 왜 더 비싼가요 21f032cbb6d4812fb496da5ee7a34a05.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 바퀴 증정되나요 기본바퀴와 증정바 21f032cbb6d48136aa89c1dbadde1e97.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 박스 크기 및 무게 21f032cbb6d48103bd5fe43e67a8d095.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 박스가 없는데 어떻게 환불 하죠 21f032cbb6d4815bacceca958ba71f8d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 법인으로 구매 원해요 21f032cbb6d48164bbe2dce71bf348d9.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 변심반품입금안내 21f032cbb6d481d7accbc1ed9ddb9e04.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 변심반품입금확인완료 21f032cbb6d481f28930dc64dfd2a1b3.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 붐코리아 사라지면 as 어떻게 받죠 21f032cbb6d4818b8d8bebeeed50e422.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 사이즈 교환 안내 21f032cbb6d481a7a1c7d4aaaef366f0.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 사이즈 추천 21f032cbb6d4810d990cfcd2af1ae4dc.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 사이즈 확인방법 21f032cbb6d481db9ad9d0662515f27e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 설명서가 없어요 조작 방법을 모ᄅ 21f032cbb6d481b09281e4cef248d494.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 설치기사방문여부=방문X 21f032cbb6d4817c9602e8fc45c571be.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 소가죽 부위 재질 안내 21f032cbb6d4819aa837d4eb21272a29.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 시리얼넘버가 있나요 21f032cbb6d4812b9551efb20023bf0f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 알루미늄 산화 21f032cbb6d4816b85b6fdb345aa0774.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 언제 와요 “모를때” 21f032cbb6d481bfa9dfc2303b1b6d2d.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 언제 와요 “알때” 21f032cbb6d481a7b65bea9ed5100461.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 에어론과 뉴에어론의 차이 21f032cbb6d4814e8399d118d3e0d7ec.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 엘레베이터가 없다면 1층 배송 21f032cbb6d4813083f9cb1037a0a282.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 오늘 패킹 내일 출고 21f032cbb6d481f6ba69ca390b0d6d99.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 오발프레임 분해 조립 21f032cbb6d481729e7ef99e27ca98d6.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 오배송 교환진행 21f032cbb6d481469091fff946360d43.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 오배송 바코드확인요청 21f032cbb6d481cea6f0cc4893d867d0.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 의자조립필요여부=완조립 21f032cbb6d481cf88c2ea8e1ddc3242.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 이거 세관문자 맞나요 21f032cbb6d4816c9d99f1ea2359fd8f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 재고 있어 바로 출고 가능 21f032cbb6d481328243e425e8492017.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 정품안내 21f032cbb6d4818f84adf4d66647c8bd.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 좌석 높이 조절 21f032cbb6d48122bbbfd85a24831771.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 좌판 분해 조립 21f032cbb6d48184939af8166a4f68ee.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 착불비 얼마예요 21f032cbb6d481b697f5c64696e917a4.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 착불비 영수증 21f032cbb6d481a3ad34ce4c915a4f3f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 출고 되었는데 배송흐름 송장번ᄒ 21f032cbb6d481839452d146255af30f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 틸팅 각도(후방 전방) 21f032cbb6d4817c8decebe4c69b0bd2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 틸팅레버가 뻑뻑해요 윤활유발송 21f032cbb6d4818b9536f60032eeb506.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 틸팅박스 유격 정상 21f032cbb6d481e3b696c4183b711724.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 틸팅박스 제대로 닫혀있는지 확이 21f032cbb6d48152977bc4c201d96e87.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 팔걸이 각도 조절 21f032cbb6d48149bc3ff977a14d176c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 팔걸이 높이조절 고정 안됨 21f032cbb6d481cfa19ac1b45e56def2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 팔걸이 좌우각도조절 고정 안됨 21f032cbb6d48126b821de1ad18b9f4f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 팔걸이 파손 (부품발송) 21f032cbb6d4817fb9c4dfa776e4d5ad.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 풀체어와 CD체어 차이점 21f032cbb6d4810ab937e8df0c1aa5ab.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 하자 반품검수 21f032cbb6d48121b61afe7ab540dd5e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 하자 반품교환 21f032cbb6d48115b9f2f5b1f59e1c8b.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 하자 반품환불 21f032cbb6d481fc9d6ac5ec3846edb2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 하자 사진 혹은 영상 확인이 필ᄋ 21f032cbb6d4812b91b2c1c5dc5f301c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 해외전용 구매 전 관부가세 비용 +  21f032cbb6d4814ebcabdb43403d42c3.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 해외직구 단순변심비용안내 21f032cbb6d4813fa7e0deaab466bf8e.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 헤드레스트 변심반품비안내 21f032cbb6d48105ba0ada1a892bc63c.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 헤드레스트 정품인가요 21f032cbb6d4818387c5d8a03bfdac9f.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 헤드레스트 조립방법 21f032cbb6d481f9a990de90da59c8e4.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 헤드레스트 플로어휠 as 불가 (해외지 21f032cbb6d481088969dd087f768ae2.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\허먼밀러) 헤드레스트만 왔어요 의자만 왔어요 21f032cbb6d481e7b0f6f113a249a826.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\현금영수증(세금계산서) 발행 가능 21f032cbb6d4812388e6d2211a9b1330.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\현금영수증(세금계산서) 발행 불가 21f032cbb6d4819bba2dc658aa729f64.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\환불되었습니다 21f032cbb6d48117ad6de84d4df36b15.md\n", "📄 병합 중: c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4TEAM\\data\\raw_docs\\qna_raw\\환불승인했는데 계좌 카드 환불 안됐다고 ᄒ 21f032cbb6d481a0b5c8c999b790d8c5.md\n", "✅ 병합 완료\n"]}], "source": ["from pathlib import Path\n", "import os\n", "\n", "base_dir = Path.cwd().parents[1] / \"SKN13-3rd-4TEAM\" / \"data\" / \"raw_docs\" / \"qna_raw\"\n", "print(f\"🔍 탐색 경로: {base_dir}\")\n", "\n", "merged_text = \"\"\n", "\n", "for root, _, files in os.walk(base_dir):\n", "    for file in files:\n", "        if file.lower().endswith(\".md\"):\n", "            path = os.path.join(root, file)\n", "            print(f\"📄 병합 중: {path}\")\n", "            with open(path, \"r\", encoding=\"utf-8\") as f:\n", "                content = f.read()\n", "                merged_text += f\"\\n\\n{content}\"\n", "\n", "print(\"✅ 병합 완료\" if merged_text.strip() else \"⚠ 병합된 내용 없음\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "92a186f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["WindowsPath('c:/Users/<USER>/Documents/Project/SKN13-3rd-4Team')"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["root_dir = Path.cwd().parents[0]\n", "root_dir"]}, {"cell_type": "code", "execution_count": 28, "id": "ce84932b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 병합 완료: 저장 위치 → c:\\Users\\<USER>\\Documents\\Project\\SKN13-3rd-4Team\\data\\merged_docs\\merged.md\n"]}], "source": ["# 병합된 내용을 저장할 경로 설정\n", "output_path = root_dir / \"data\" / \"merged_docs\" / \"merged.md\"\n", "\n", "# 폴더 없으면 생성\n", "output_path.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "# 파일로 저장\n", "with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "    f.write(merged_text)\n", "\n", "print(f\"✅ 병합 완료: 저장 위치 → {output_path}\")"]}, {"cell_type": "code", "execution_count": 21, "id": "6da45a56", "metadata": {}, "outputs": [{"data": {"text/plain": ["87572"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(merged_text)"]}, {"cell_type": "code", "execution_count": 34, "id": "8da498e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✂️ 텍스트 분할 중...\n"]}], "source": ["\n", "print(\"✂️ 텍스트 분할 중...\")\n", "chunks = split_by_title(merged_text)\n"]}, {"cell_type": "code", "execution_count": 37, "id": "45205d9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["['# (구매 후 바로) 양품, 검수해서 보내주세요.\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 배송\\n해외: 국내, 해외\\n\\n안녕하세요\\n구매해주셔서 감사합니다.\\n양품으로 최대한 빠르게 보내드리도록 노력하겠습니다.\\n감사합니다:)',\n", " '# 7179 내부 사이즈\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 7179, 상품\\n해외: 국내\\n\\n안녕하세요.\\n소형 내부 높이는 22cm, 가로 27.8cm, 세로 20.5cm 입니다.\\n대형 내부 높이는 26.5cm, 가로 30.4cm, 세로 20.5cm 입니다.\\n감사합니다.',\n", " '# LT2 업데이트 파일\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 빔프로젝트\\n해외: 해외\\n\\n안녕하세요.\\nLT2 경우 앱스토어에서 어플 업데이트 해주시면 기본적으로 다시 ott 버전이 업데이트가 됩니다.\\n만약 앱스토어에서 재 설치를 하였는데도 ott가 접속이 안되신다면 아래의 최신 업데이트 파일 다운로드 해서 재설치 해보시기 바랍니다.\\n[https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view](https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view)\\n구글 드라이브 링크입니다.\\nzip안에 설치방법 확인하신 후 설치 부탁드립니다.\\n감사합니다.',\n", " '# LT2 업데이트 해도 안됩니다.\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 빔프로젝트\\n해외: 해외\\n\\n안녕하세요.\\nLT2 경우 앱스토어에서 어플 업데이트 해주시면 기본적으로 다시 ott 버전이 업데이트가 됩니다.\\n만약 앱스토어에서 재 설치를 하였는데도 ott가 접속이 안되신다면 아래의 최신 업데이트 파일 다운로드 해서 재설치 해보시기 바랍니다.\\n[https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view](https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view)\\n구글 드라이브 링크입니다.\\nzip안에 설치방법 확인하신 후 설치 부탁드립니다.\\n위의 링크로도 업데이트가 되지 않으실 경우 제공가능한 다른 버전이 없으니\\n사설 업체 방문하여 수리 및 확인 부탁드립니다.\\n감사합니다.\\n감사합니다.',\n", " '# X6 어플 업데이트\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 빔프로젝트\\n해외: 해외\\n\\n안녕하세요.\\nX6 제품은 단종되어 더 이상의 업데이트를 지원하지 않습니다.\\n아래의 구글 드라이브 링크의 apk가 마지막 업데이트 버전입니다.\\n[https://drive.google.com/file/d/1lgQSlWJjz4Jndqd0ZrZMndff1fl_zoPm/view?usp=drive_link](https://drive.google.com/file/d/1lgQSlWJjz4Jndqd0ZrZMndff1fl_zoPm/view?usp=drive_link)\\n확인 부탁드립니다.\\n감사합니다.',\n", " '# X6 어플 업데이트 해도 안됩니다.\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 빔프로젝트\\n해외: 해외\\n\\n안녕하세요.\\nX6 제품은 단종되어 더 이상의 업데이트를 지원하지 않습니다.\\n아래의 구글 드라이브 링크의 apk가 마지막 업데이트 버전입니다.\\n[https://drive.google.com/file/d/1lgQSlWJjz4Jndqd0ZrZMndff1fl_zoPm/view?usp=drive_link](https://drive.google.com/file/d/1lgQSlWJjz4Jndqd0ZrZMndff1fl_zoPm/view?usp=drive_link)\\n위의 링크로도 업데이트가 되지 않으실 경우 제공가능한 다른 버전이 없으니\\n사설 업체 방문하여 수리 및 확인 부탁드립니다.\\n감사합니다.',\n", " '# X6 전원 어댑터 스펙\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 빔프로젝트\\n해외: 해외\\n\\n안녕하세요.\\nX6 제품은 단종되어 더 이상의 관련 부품이 없습니다.\\n전원 어댑터 경우\\n100-240V~50/60Hz 1.5A 스펙이였기 때문에 해당 스팩 참고부탁드립니다.\\n감사합니다.',\n", " '# 검수문제없음) 반송/반품배송비 청구\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내, 해외\\n붐코계좌: 국민 43180101200295 (주)붐코리아\\n나스계좌: 국민 27090104200646 나인스퀘어\\n이오계좌: 카뱅 3333063649738 권은지\\n랩99계좌: 국민 69920101314635 주식회사 랩99\\n드림계좌: 카뱅 3333063649738 권은지\\n\\n안녕하세요.\\n해당 상품 [**불안들어옴**] 사유로 반품해주셨는데 검수 해보니 아무 이상 없는 것으로 확인됩니다.\\n해당 건 검수 영상 제공 가능합니다.  필요하시다면 이메일 주소 회신 부탁드립니다.\\n이메일로 영상 전달 드리도록 하겠습니다.\\n해당 제품은 검수 결과 문제가 없는 것으로 판단되었기 때문에 초반 안내 드린 것처럼 변심 반품 처리 혹은 반송하고자 합니다.\\n변심 반품 처리 원하실 경우 : **변심반품비10000원**\\n반송 처리 원하실 경우 : **반송비 10000원**\\n계좌번호 : **국민 43180101200295 (주)붐코리아**\\n로 입금 부탁드리며 안내된 계좌로 입금 하셨다면 입금자명 문의글로 회신 부탁드립니다.\\n입금 확인 후 이후 클레임 완료 처리 도와드리겠습니다.\\n감사합니다.',\n", " '# 계좌입금하도록 하겠습니다.\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내, 해외\\n\\n안녕하세요.\\n알려주신 계좌로 영업일 기준 1-2일 내에 입금처리 하도록 하겠습니다.\\n감사합니다.',\n", " '# 고객귀책으로 잘못 선택해서 일부만 환불 받은 고객에게 계좌환급 할때\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내, 해외\\n붐코계좌: 국민 43180101200295 (주)붐코리아\\n나스계좌: 국민 27090104200646 나인스퀘어\\n이오계좌: 카뱅 3333063649738 권은지\\n랩99계좌: 국민 69920101314635 주식회사 랩99\\n드림계좌: 카뱅 3333063649738 권은지\\n\\n안녕하세요.\\n확인 결과 반품 접수 시 고객귀책으로 귀책사유 신청해주셔서 시스템에서 자동으로 반품비 차감 후 환불이 된 것 같습니다.\\n반품비로 환불 못 받으신 금액은 고객님의 계좌번호, 은행명, 예금주명 알려주시면 해당 계좌로 환급해드리겠습니다.\\n회신 부탁드립니다.\\n감사합니다.',\n", " '# 고스트 투명 아크릴 거실 사이드테이블 소파 보조테이블 65cm 두께 문의\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 상품\\n해외: 국내\\n\\n안녕하세요.\\n두께는 대략 1CM 정도됩니다.\\n감사합니다.',\n", " '# 국내 부품 발송해드려도 될까요?\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내\\n붐코계좌: 국민 43180101200295 (주)붐코리아\\n나스계좌: 국민 27090104200646 나인스퀘어\\n이오계좌: 카뱅 3333063649738 권은지\\n랩99계좌: 국민 69920101314635 주식회사 랩99\\n드림계좌: 카뱅 3333063649738 권은지\\n\\n안녕하세요.\\n불편을 드려 정말 죄송합니다.\\n문제 확인하였으며 해당 부분 부품 교체 가능한 상황이라, 부품 재발송 가능한데 부품 재발송으로 처리 도와드려도 될까요?\\n국내에서 배송 될 예정이며 (영업일 기준) 1-2일 걸릴 예정입니다.\\n부품 발송에 동의하신다면 부품 재발송해드리겠습니다.\\n회신 부탁드립니다.\\n감사합니다.',\n", " '# 국내, 교환 재고 부족\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내\\n붐코계좌: 국민 43180101200295 (주)붐코리아\\n나스계좌: 국민 27090104200646 나인스퀘어\\n이오계좌: 카뱅 3333063649738 권은지\\n랩99계좌: 국민 69920101314635 주식회사 랩99\\n드림계좌: 카뱅 3333063649738 권은지\\n\\n안녕하세요.\\n교환 접수는 이미 되어있으나 현재 재고가 부족하여 출고 대기 중입니다.\\n재고는 **5월말**에 추가 입고 될 예정이며 재고가 추가 입고되면 바로 발송해드리겠습니다.\\n불편을 드려 정말 죄송하며 조금만 더 시일 양해 부탁드립니다.\\n감사합니다.',\n", " '# 국내배송, 언제 배송되나요? → 예정일을 모를 때\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 배송\\n해외: 국내\\n\\n안녕하세요.\\n해당 제품 많은 관심과 사랑 덕분에 예상보다 주문량이 많아 준비된 재고가 부족하여 배송 지연 되고 있습니다.\\n고객님께 불편 드려 정말 죄송합니다. \\n공급이 부족했던 재고 경우 현재 빠른 시일 내로 추가 입고 될 예정이며  예상 출고일은 **7월 초** 입니다. \\n아직까지 현재 정확한 배송 일정이 확정되지 않아 대략적인 시기 외 자세한 일정 안내가 어렵습니다.\\n일정이 정확히 확정되면 별도로 문자 안내 보내드리도록 하겠습니다. \\n너그럽게 조금만 더 기다려주시기 바랍니다. \\n다시 한번 빠른 출고 도와드리지 못한 점 깊은 사과 말씀 드리며, 앞으로 더욱 좋은 상품과 좋은 퀄리티, 빠른 배송으로 고객님의 성원에 보답 드릴 수 있도록 노력하겠습니다.\\n감사합니다.',\n", " '# 국내배송, 언제 배송되나요? → 예정일을 알때\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 배송\\n해외: 국내\\n\\n안녕하세요.\\n해당 제품 많은 관심과 사랑 덕분에 예상보다 주문량이 많아 준비된 재고가 부족하여 배송 지연 되고 있습니다. 고객님께 불편 드려 정말 죄송합니다. \\n공급이 부족했던 재고 경우 현재 빠른 시일 내로 추가 입고 될 예정이며 **6월 30일**에 출고 될 예정입니다.\\n시일 양해 부탁드립니다.\\n다시 한번 빠른 출고 도와드리지 못한 점 깊은 사과 말씀 드리며, 앞으로 더욱 좋은 상품과 좋은 퀄리티, 빠른 배송으로 고객님의 성원에 보답 드릴 수 있도록 노력하겠습니다.\\n감사합니다.',\n", " '# 국내부품발송 송장전달\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내, 해외\\n\\n안녕하세요.\\n**CJ대한통운 송장번호 :**\\n로 국내 발송해드리겠습니다.\\n감사합니다.',\n", " '# 단순변심) 단순변심반품비 얼마예요\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 국내, 해외\\n붐코계좌: 국민 43180101200295 (주)붐코리아\\n나스계좌: 국민 27090104200646 나인스퀘어\\n이오계좌: 카뱅 3333063649738 권은지\\n랩99계좌: 국민 69920101314635 주식회사 랩99\\n드림계좌: 카뱅 3333063649738 권은지\\n\\n안녕하세요.\\n단순변심으로 반품하실 경우 변심비는 **40000원** 입니다.\\n(구성품/부자재 누락이 없고 사용 하지 않은 상품일 경우만 변심반품 가능합니다.\\n회수 후 사용 흔적이 있다면 변심반품이 거절되며 반송될 수 있고 반송이 되면 이후 클레임 처리 불가합니다.)\\n단순변심 반품처리 원하신다면 안내 드린 비용을 아래의 계좌 입금 후 회신 부탁드립니다.\\n**계좌정보 :** \\n감사합니다.',\n", " '# 단순변심) 반품수거하도록 하겠습니\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 해외\\n붐코계좌: 국민 43180101200295 (주)붐코리아\\n나스계좌: 국민 27090104200646 나인스퀘어\\n이오계좌: 카뱅 3333063649738 권은지\\n랩99계좌: 국민 69920101314635 주식회사 랩99\\n드림계좌: 카뱅 3333063649738 권은지\\n\\n안녕하세요.\\n빠르게 반품 회수 해 환불로 진행 도와드리겠습니다.\\n익일 반품 접수하여 영업일 기준 1-2일 내로 택배기사님께서 방문하셔서 물품 수거할 예정이니 기사님께서 언제든지 물품 수거 가능할 수 있도록 물품 재포장하여 준비 부탁드립니다.\\n1.  되도록 포장박스 외부에 “반품/교환” 등의 문구를 적지 마시고 포스트잇 등으로 추후 땔 수 있도록 진행 부탁 드립니다.\\n(이미 표기 하신 경우 어쩔 수 없으나 박스에 너무 많은 표기는 지양해주시기 바랍니다)\\n2. 재포장 시 원래 포장지 그대로 포장해주셔야 하며, 같이 보내드린 포장재 및 구성품 중 일부가 누락된다면 추후 환불/교환이 어려우실 수 있으니 작은 구성품 하나도 모두 원래 받으신 그대로 보존하여 재포장 부탁드립니다.\\n(만약 회수 후 검수 과정에서 문제점이 발견될 시 물품 반송 혹은 별도 보상 청구를 할 수 있습니다. 유의 부탁 드립니다.)\\n반품 환불 경우 반품 택배 수거 완료 시 제품 검수 후 별다른 문제가 없다면 자동 환불 처리 될 예정이니 참고 부탁 드립니다.\\n감사합니다.',\n", " '# 단순변심) 배송지연인데 왜 단순변심인가요?\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 해외\\n\\n안녕하세요.\\n해당 건 반품 접수 하셨을때는 이미 출고된 상황이었습니다.\\n출고 전 상황이라면 취소로 간주하여 무상환불 가능하나 이미 출고된 경우 해외배송비가 발생하기 때문에 무상 반품은 어렵습니다.\\n현재 환불을 원하시면 반품 진행하여야 하며 이때 반품왕복배송비는 고객님께서 납부해주셔야 처리 가능합니다.\\n이 점은 상세페이지 노티스에 배송관련 부분 보시면 물품이 이미 해외 출고가 되었다면 무상 주문취소 불가하며 해외왕복반품비가 발생한다고 적혀있으며 배송지연 역시 해외 판매처의 사정에 의해 배송지연이 될 수 있다고 적혀있습니다.\\n상세페이지 참고 부탁드리며 고객님의 너그러운 양해 부탁드립니다.\\n감사합니다.',\n", " '# 단순변심) 해외 제품 반품비용 왜 이렇게 비싸요?\\n\\n생성일: 2025년 6월 27일 오전 10:09\\n유형: 클레임\\n해외: 해외\\n\\n안녕하세요.\\n해당 건 해외직구 상품이기 때문에 국제 배송 출고비와 반품비가 합산되어 청구되기 때문에 일반 국내반품보다 비용이 비쌉니다.\\n반품비의 경우 상세페이지 배송/환불 금액에 이미 고지가 되어있는 관계로 해당 금액대로 입금해주셔야지만 반품 진행 가능한다는 점 양해 부탁드립니다. \\n감사합니다.']"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["chunks[:20]"]}, {"cell_type": "code", "execution_count": 38, "id": "6433960e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💡 질문 생성 중...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18832\\2990547196.py:15: LangChainDeprecationWarning: The class `ChatOpenAI` was deprecated in LangChain 0.0.10 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-openai package and should be used instead. To use it run `pip install -U :class:`~langchain-openai` and import as `from :class:`~langchain_openai import ChatOpenAI``.\n", "  llm = ChatOpenAI(model_name=\"gpt-4\", temperature=0.3)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18832\\2990547196.py:16: LangChainDeprecationWarning: The class `<PERSON><PERSON>hain` was deprecated in LangChain 0.1.17 and will be removed in 1.0. Use :meth:`~RunnableSequence, e.g., `prompt | llm`` instead.\n", "  return LLMChain(prompt=prompt, llm=llm)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18832\\804560214.py:4: LangChainDeprecationWarning: The method `Chain.run` was deprecated in langchain 0.1.0 and will be removed in 1.0. Use :meth:`~invoke` instead.\n", "  qa = chain.run(answer=chunk)\n"]}], "source": ["\n", "print(\"💡 질문 생성 중...\")\n", "chain = build_qa_chain()\n", "docs = generate_documents(chunks, chain)\n"]}, {"cell_type": "code", "execution_count": 40, "id": "d5888370", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔹 Document 1:\n", "Q: 제가 구매한 제품을 검수 후에 보내주실 수 있나요?\n", "A: 안녕하세요\n", "구매해주셔서 감사합니다.\n", "양품으로 최대한 빠르게 보내드리도록 노력하겠습니다.\n", "감사합니다:)\n", "------------------------------------------------------------\n", "\n", "🔹 Document 2:\n", "Q: 7179 상품의 소형과 대형 내부 사이즈가 어떻게 되나요?\n", "A: 안녕하세요.\n", "소형 내부 높이는 22cm, 가로 27.8cm, 세로 20.5cm 입니다.\n", "대형 내부 높이는 26.5cm, 가로 30.4cm, 세로 20.5cm 입니다.\n", "감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 3:\n", "Q: LT2 앱의 ott 버전이 업데이트가 안되는데 어떻게 해야 하나요?\n", "A: LT2 경우 앱스토어에서 어플 업데이트 해주시면 기본적으로 다시 ott 버전이 업데이트가 됩니다. 만약 앱스토어에서 재 설치를 하였는데도 ott가 접속이 안되신다면 아래의 최신 업데이트 파일 다운로드 해서 재설치 해보시기 바랍니다. [https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view](https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view) 구글 드라이브 링크입니다. zip안에 설치방법 확인하신 후 설치 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 4:\n", "Q: LT2를 업데이트하려고 하는데 작동이 안되네요. 어떻게 해야하나요?\n", "A: 안녕하세요. LT2 경우 앱스토어에서 어플 업데이트 해주시면 기본적으로 다시 ott 버전이 업데이트가 됩니다. 만약 앱스토어에서 재 설치를 하였는데도 ott가 접속이 안되신다면 아래의 최신 업데이트 파일 다운로드 해서 재설치 해보시기 바랍니다. [https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view](https://drive.google.com/file/d/1eZBaBn9W6Nt48jDkSZ3sCmUJJ3IOBEDp/view) 구글 드라이브 링크입니다. zip안에 설치방법 확인하신 후 설치 부탁드립니다. 위의 링크로도 업데이트가 되지 않으실 경우 제공가능한 다른 버전이 없으니 사설 업체 방문하여 수리 및 확인 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 5:\n", "Q: X6 어플에 대한 최신 업데이트 정보를 알고 싶습니다.\n", "A: X6 제품은 단종되어 더 이상의 업데이트를 지원하지 않습니다. 아래의 구글 드라이브 링크의 apk가 마지막 업데이트 버전입니다. [https://drive.google.com/file/d/1lgQSlWJjz4Jndqd0ZrZMndff1fl_zoPm/view?usp=drive_link](https://drive.google.com/file/d/1lgQSlWJjz4Jndqd0ZrZMndff1fl_zoPm/view?usp=drive_link) 확인 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 6:\n", "Q: X6 어플을 업데이트하려고 하는데 업데이트가 되지 않습니다. 어떻게 해야 하나요?\n", "A: X6 제품은 단종되어 더 이상의 업데이트를 지원하지 않습니다. 아래의 구글 드라이브 링크의 apk가 마지막 업데이트 버전입니다. 위의 링크로도 업데이트가 되지 않으실 경우 제공가능한 다른 버전이 없으니 사설 업체 방문하여 수리 및 확인 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 7:\n", "Q: X6 빔프로젝트의 전원 어댑터 스펙을 알려주실 수 있나요?\n", "A: 안녕하세요. X6 제품은 단종되어 더 이상의 관련 부품이 없습니다. 전원 어댑터 경우 100-240V~50/60Hz 1.5A 스펙이였기 때문에 해당 스팩 참고부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 8:\n", "Q: 제가 반품한 상품에 문제가 없다고 하셨는데, 어떻게 확인하셨나요? 그리고 이제 어떻게 해야 하나요?\n", "A: 안녕하세요. 해당 상품 [**불안들어옴**] 사유로 반품해주셨는데 검수 해보니 아무 이상 없는 것으로 확인됩니다. 해당 건 검수 영상 제공 가능합니다. 필요하시다면 이메일 주소 회신 부탁드립니다. 이메일로 영상 전달 드리도록 하겠습니다. 해당 제품은 검수 결과 문제가 없는 것으로 판단되었기 때문에 초반 안내 드린 것처럼 변심 반품 처리 혹은 반송하고자 합니다. 변심 반품 처리 원하실 경우 : **변심반품비10000원** 반송 처리 원하실 경우 : **반송비 10000원** 계좌번호 : **국민 43180101200295 (주)붐코리아**로 입금 부탁드리며 안내된 계좌로 입금 하셨다면 입금자명 문의글로 회신 부탁드립니다. 입금 확인 후 이후 클레임 완료 처리 도와드리겠습니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 9:\n", "Q: 환불 처리는 어떻게 진행되나요?\n", "A: 안녕하세요. 알려주신 계좌로 영업일 기준 1-2일 내에 입금처리 하도록 하겠습니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 10:\n", "Q: 제가 반품을 신청했는데, 환불받은 금액이 제가 계산한 것보다 적습니다. 이유가 무엇인가요? 그리고 나머지 금액은 어떻게 환불받을 수 있나요?\n", "A: 안녕하세요. 확인 결과 반품 접수 시 고객귀책으로 귀책사유 신청해주셔서 시스템에서 자동으로 반품비 차감 후 환불이 된 것 같습니다. 반품비로 환불 못 받으신 금액은 고객님의 계좌번호, 은행명, 예금주명 알려주시면 해당 계좌로 환급해드리겠습니다. 회신 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 11:\n", "Q: 고스트 투명 아크릴 거실 사이드테이블 소파 보조테이블 65cm의 두께는 얼마나 되나요?\n", "A: 두께는 대략 1CM 정도됩니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 12:\n", "Q: 제품에 문제가 있어서 부품을 교체해야 하는데, 해외에 계신데도 교체 부품을 보내주실 수 있나요?\n", "A: 안녕하세요. 불편을 드려 정말 죄송합니다. 문제 확인하였으며 해당 부분 부품 교체 가능한 상황이라, 부품 재발송 가능한데 부품 재발송으로 처리 도와드려도 될까요? 국내에서 배송 될 예정이며 (영업일 기준) 1-2일 걸릴 예정입니다. 부품 발송에 동의하신다면 부품 재발송해드리겠습니다. 회신 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 13:\n", "Q: 제가 요청한 제품 교환은 언제쯤 진행될 수 있을까요? 재고가 언제 들어오는지 알 수 있을까요?\n", "A: 안녕하세요. 교환 접수는 이미 되어있으나 현재 재고가 부족하여 출고 대기 중입니다. 재고는 5월말에 추가 입고 될 예정이며 재고가 추가 입고되면 바로 발송해드리겠습니다. 불편을 드려 정말 죄송하며 조금만 더 시일 양해 부탁드립니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 14:\n", "Q: 제가 주문한 제품의 배송일이 언제인지 알 수 있을까요?\n", "A: 해당 제품 많은 관심과 사랑 덕분에 예상보다 주문량이 많아 준비된 재고가 부족하여 배송 지연 되고 있습니다. 고객님께 불편 드려 정말 죄송합니다. 공급이 부족했던 재고 경우 현재 빠른 시일 내로 추가 입고 될 예정이며  예상 출고일은 7월 초 입니다. 아직까지 현재 정확한 배송 일정이 확정되지 않아 대략적인 시기 외 자세한 일정 안내가 어렵습니다. 일정이 정확히 확정되면 별도로 문자 안내 보내드리도록 하겠습니다. 너그럽게 조금만 더 기다려주시기 바랍니다. 다시 한번 빠른 출고 도와드리지 못한 점 깊은 사과 말씀 드리며, 앞으로 더욱 좋은 상품과 좋은 퀄리티, 빠른 배송으로 고객님의 성원에 보답 드릴 수 있도록 노력하겠습니다. 감사합니다.\n", "------------------------------------------------------------\n", "\n", "🔹 Document 15:\n", "Q: 제가 주문한 제품이 언제 도착할 예정인지 알 수 있을까요?\n", "A: 해당 제품 많은 관심과 사랑 덕분에 예상보다 주문량이 많아 준비된 재고가 부족하여 배송 지연 되고 있습니다. 고객님께 불편 드려 정말 죄송합니다. 공급이 부족했던 재고 경우 현재 빠른 시일 내로 추가 입고 될 예정이며 6월 30일에 출고 될 예정입니다. 시일 양해 부탁드립니다. 다시 한번 빠른 출고 도와드리지 못한 점 깊은 사과 말씀 드리며, 앞으로 더욱 좋은 상품과 좋은 퀄리티, 빠른 배송으로 고객님의 성원에 보답 드릴 수 있도록 노력하겠습니다. 감사합니다.\n", "------------------------------------------------------------\n"]}], "source": ["for i, doc in enumerate(docs[:15]):  # 앞부분만 샘플 확인\n", "    print(f\"\\n🔹 Document {i+1}:\\n{doc.page_content}\\n{'-'*60}\")"]}, {"cell_type": "code", "execution_count": 42, "id": "fcae3df7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 Chroma 저장 중...\n", "✅ 완료: 총 문서 수 = 200\n"]}], "source": ["\n", "print(\"📦 Chroma 저장 중...\")\n", "store_to_chroma(docs, persist_dir=\"../data/vectordb_chroma\")\n", "\n", "print(\"✅ 완료: 총 문서 수 =\", len(docs))"]}, {"cell_type": "code", "execution_count": null, "id": "63eb127a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "6aa53e48", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: google-cloud-vision in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (3.10.2)\n", "Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (2.25.0)\n", "Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from google-cloud-vision) (2.40.3)\n", "Collecting proto-plus<2.0.0,>=1.22.3 (from google-cloud-vision)\n", "  Using cached proto_plus-1.26.1-py3-none-any.whl.metadata (2.2 kB)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from google-cloud-vision) (5.29.3)\n", "Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (1.70.0)\n", "Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (2.32.4)\n", "Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (1.73.0)\n", "Collecting grpcio-status<2.0.0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision)\n", "  Downloading grpcio_status-1.73.1-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-cloud-vision) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-cloud-vision) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-cloud-vision) (4.9.1)\n", "Collecting protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 (from google-cloud-vision)\n", "  Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl.metadata (593 bytes)\n", "Collecting grpcio<2.0.0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision)\n", "  Downloading grpcio-1.73.1-cp312-cp312-win_amd64.whl.metadata (4.0 kB)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-cloud-vision) (2025.6.15)\n", "Requirement already satisfied: pyasn1>=0.1.3 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\3rd\\lib\\site-packages (from rsa<5,>=3.1.4->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-cloud-vision) (0.6.1)\n", "Downloading grpcio_status-1.73.1-py3-none-any.whl (14 kB)\n", "Downloading grpcio-1.73.1-cp312-cp312-win_amd64.whl (4.3 MB)\n", "   ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "   ---------------------------------------- 4.3/4.3 MB 64.6 MB/s eta 0:00:00\n", "Using cached proto_plus-1.26.1-py3-none-any.whl (50 kB)\n", "Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl (435 kB)\n", "Installing collected packages: protobuf, grpcio, proto-plus, grpcio-status\n", "\n", "  Attempting uninstall: protobuf\n", "\n", "    Found existing installation: protobuf 5.29.3\n", "\n", "    Uninstalling protobuf-5.29.3:\n", "\n", "      Successfully uninstalled protobuf-5.29.3\n", "\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------------------------------------- 0/4 [protobuf]\n", "  Attempting uninstall: grp<PERSON>\n", "   ---------------------------------------- 0/4 [protobuf]\n", "    Found existing installation: grpcio 1.73.0\n", "   ---------------------------------------- 0/4 [protobuf]\n", "    Uninstalling grpcio-1.73.0:\n", "   ---------------------------------------- 0/4 [protobuf]\n", "      Successfully uninstalled grpcio-1.73.0\n", "   ---------------------------------------- 0/4 [protobuf]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   ---------- ----------------------------- 1/4 [grpcio]\n", "   -------------------- ------------------- 2/4 [proto-plus]\n", "   -------------------- ------------------- 2/4 [proto-plus]\n", "   -------------------- ------------------- 2/4 [proto-plus]\n", "   ---------------------------------------- 4/4 [grpcio-status]\n", "\n", "Successfully installed grpcio-1.71.0 grpcio-status-1.73.1 proto-plus-1.26.1 protobuf-6.31.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "unstructured-inference 1.0.5 requires accelerate, which is not installed.\n", "unstructured-inference 1.0.5 requires pdfminer-six, which is not installed.\n", "unstructured-inference 1.0.5 requires pypdfium2, which is not installed.\n", "unstructured-inference 1.0.5 requires rapidfuzz, which is not installed.\n", "opentelemetry-proto 1.34.1 requires protobuf<6.0,>=5.0, but you have protobuf 6.31.1 which is incompatible.\n"]}], "source": ["#OCR테스트 \n", "%pip install google-cloud-vision\n"]}, {"cell_type": "code", "execution_count": 10, "id": "fb96390c", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"GOOGLE_APPLICATION_CREDENTIALS\"] = \"credential.json\""]}, {"cell_type": "code", "execution_count": 11, "id": "132be170", "metadata": {}, "outputs": [], "source": ["from google.cloud import vision\n", "from google.cloud.vision_v1 import types\n", "\n", "def google_vision_ocr(image_path):\n", "    client = vision.ImageAnnotatorClient()\n", "    \n", "    with open(image_path, 'rb') as img_file:\n", "        content = img_file.read()\n", "\n", "    image = vision.Image(content=content)\n", "    response = client.text_detection(image=image)\n", "    texts = response.text_annotations\n", "\n", "    if not texts:\n", "        return \"❌ 텍스트 인식 실패\"\n", "\n", "    return texts[0].description.strip()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "027b3e5b", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'text.jpg'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[12]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m result = \u001b[43mgoogle_vision_ocr\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtext.jpg\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m🧾 OCR 결과:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m, result)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 7\u001b[39m, in \u001b[36mgoogle_vision_ocr\u001b[39m\u001b[34m(image_path)\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgoogle_vision_ocr\u001b[39m(image_path):\n\u001b[32m      5\u001b[39m     client = vision.ImageAnnotatorClient()\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mimage_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mrb\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m img_file:\n\u001b[32m      8\u001b[39m         content = img_file.read()\n\u001b[32m     10\u001b[39m     image = vision.Image(content=content)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\3rd\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:327\u001b[39m, in \u001b[36m_modified_open\u001b[39m\u001b[34m(file, *args, **kwargs)\u001b[39m\n\u001b[32m    320\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m file \u001b[38;5;129;01min\u001b[39;00m {\u001b[32m0\u001b[39m, \u001b[32m1\u001b[39m, \u001b[32m2\u001b[39m}:\n\u001b[32m    321\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    322\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mIPython won\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt let you open fd=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m by default \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    323\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    324\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33myou can use builtins\u001b[39m\u001b[33m'\u001b[39m\u001b[33m open.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    325\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m327\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mio_open\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'text.jpg'"]}], "source": ["result = google_vision_ocr(\"test.jpg\")\n", "print(\"🧾 OCR 결과:\\n\", result)\n"]}], "metadata": {"kernelspec": {"display_name": "3rd", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}