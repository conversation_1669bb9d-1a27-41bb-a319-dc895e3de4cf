{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3c6954ee", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from bs4 import BeautifulSoup\n", "import time\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from textwrap import dedent\n", "from uuid import uuid4"]}, {"cell_type": "code", "execution_count": null, "id": "3c0a0031", "metadata": {}, "outputs": [], "source": ["# SSF 몰\n", "\n", "# 1. Selenium 드라이버 실행\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))\n", "url = \"https://www.lfmall.co.kr/app/product/HSTS5B206B2?has_init=Y\"\n", "# url = \"https://www.lfmall.co.kr/app/exhibition/menu/502\"\n", "url = \"https://www.lfmall.co.kr/app/product/HZTS5B894BK\"\n", "url = \"https://www.ssfshop.com/ranking?rankSect=CLICK_RANK&ctgryFlterCd=CTGRY_FEMALE&preferAgeCd=ALL&brndShopId=&brandShopNo=&dspCtgryNo=&otltYn=&cnncCtgryNo=&lagSpcltyYn=&utag=\"\n", "\n", "driver.get(url)\n", "time.sleep(3)  # 페이지 로딩 대기\n", "\n", "# 2. 스크롤 내리기\n", "last_height = driver.execute_script(\"return document.body.scrollHeight\")\n", "\n", "while True:\n", "    # 스크롤 맨 아래로 내리기\n", "    driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n", "    \n", "    # 로딩 대기\n", "    time.sleep(2)\n", "\n", "    # 현재 페이지 높이 확인\n", "    new_height = driver.execute_script(\"return document.body.scrollHeight\")\n", "    \n", "    # 더 이상 스크롤이 내려가지 않으면 종료\n", "    if new_height == last_height:\n", "        break\n", "    last_height = new_height\n", "\n", "# 3. url 리스트 만들기\n", "product_list = driver.find_elements(By.CSS_SELECTOR, \"#clickNowForm > section > div:nth-child(2) > div.list_goods > ul > li > a\")\n", "print(len(product_list))\n", "\n", "# 4. 각 url 따기\n", "urls = []\n", "for product in product_list:\n", "    url = product.get_attribute(\"href\")\n", "    urls.append(url)\n", "print(len(urls))\n"]}, {"cell_type": "code", "execution_count": null, "id": "e154d8e8", "metadata": {}, "outputs": [], "source": ["# url 저장\n", "import os\n", "os.makedirs(\"../data/raw_docs\", exist_ok=True)\n", "with open(\"../data/raw_docs/ssf_mall_urls.txt\", \"w\") as f:\n", "    f.write(\"\\n\".join(urls))"]}, {"cell_type": "code", "execution_count": 2, "id": "a71c1af7", "metadata": {}, "outputs": [], "source": ["# url 불러오기\n", "def load_urls(file_path):\n", "    with open(file_path, \"r\") as f:\n", "        urls = f.read().splitlines()\n", "    return urls\n", "urls = load_urls(\"../data/raw_docs/ssf_mall_urls.txt\")"]}, {"cell_type": "code", "execution_count": 65, "id": "f3b8ab0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1] 에러 발생: Message: invalid session id: session deleted as the browser has closed the connection\n", "from disconnected: not connected to DevTools\n", "  (Session info: chrome=137.0.7151.122)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff7dccecda5+78885]\n", "\tGetHandleVerifier [0x0x7ff7dccece00+78976]\n", "\t(No symbol) [0x0x7ff7dcaa9bca]\n", "\t(No symbol) [0x0x7ff7dca959b5]\n", "\t(No symbol) [0x0x7ff7dcaba9ca]\n", "\t(No symbol) [0x0x7ff7dcb305e5]\n", "\t(No symbol) [0x0x7ff7dcb50b42]\n", "\t(No symbol) [0x0x7ff7dcb28963]\n", "\t(No symbol) [0x0x7ff7dcaf16b1]\n", "\t(No symbol) [0x0x7ff7dcaf2443]\n", "\tGetHandleVerifier [0x0x7ff7dcfc4eed+3061101]\n", "\tGetHandleVerifier [0x0x7ff7dcfbf33d+3037629]\n", "\tGetHandleVerifier [0x0x7ff7dcfde592+3165202]\n", "\tGetHandleVerifier [0x0x7ff7dcd0730e+186766]\n", "\tGetHandleVerifier [0x0x7ff7dcd0eb3f+217535]\n", "\tGetHandleVerifier [0x0x7ff7dccf59b4+114740]\n", "\tGetHandleVerifier [0x0x7ff7dccf5b69+115177]\n", "\tGetHandleVerifier [0x0x7ff7dccdc368+10728]\n", "\tBaseThreadInitThunk [0x0x7ffbcecde8d7+23]\n", "\tRtlUserThreadStart [0x0x7ffbd017c34c+44]\n", "\n", "1번 째 상품 로딩에 5.843412399291992 초 걸렸습니다\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[65]\u001b[39m\u001b[32m, line 139\u001b[39m\n\u001b[32m    137\u001b[39m s = time.time()\n\u001b[32m    138\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00midx\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m번 째 상품 로딩에 \u001b[39m\u001b[38;5;132;01m{\u001b[39;00ms-e\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 초 걸렸습니다\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m139\u001b[39m \u001b[43mdriver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mquit\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:219\u001b[39m, in \u001b[36mChromiumDriver.quit\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    217\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Closes the browser and shuts down the ChromiumDriver executable.\"\"\"\u001b[39;00m\n\u001b[32m    218\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m219\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mquit\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    220\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[32m    221\u001b[39m     \u001b[38;5;66;03m# We don't care about the message because something probably has gone wrong\u001b[39;00m\n\u001b[32m    222\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py:605\u001b[39m, in \u001b[36mWebDriver.quit\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    598\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Quits the driver and closes every associated window.\u001b[39;00m\n\u001b[32m    599\u001b[39m \n\u001b[32m    600\u001b[39m \u001b[33;03mExample:\u001b[39;00m\n\u001b[32m    601\u001b[39m \u001b[33;03m--------\u001b[39;00m\n\u001b[32m    602\u001b[39m \u001b[33;03m>>> driver.quit()\u001b[39;00m\n\u001b[32m    603\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    604\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m605\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[43m.\u001b[49m\u001b[43mQUIT\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    606\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    607\u001b[39m     \u001b[38;5;28mself\u001b[39m.stop_client()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py:445\u001b[39m, in \u001b[36mWebDriver.execute\u001b[39m\u001b[34m(self, driver_command, params)\u001b[39m\n\u001b[32m    442\u001b[39m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33msessionId\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m params:\n\u001b[32m    443\u001b[39m         params[\u001b[33m\"\u001b[39m\u001b[33msessionId\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m.session_id\n\u001b[32m--> \u001b[39m\u001b[32m445\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver_command\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    446\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[32m    447\u001b[39m     \u001b[38;5;28mself\u001b[39m.error_handler.check_response(response)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py:404\u001b[39m, in \u001b[36mRemoteConnection.execute\u001b[39m\u001b[34m(self, command, params)\u001b[39m\n\u001b[32m    402\u001b[39m trimmed = \u001b[38;5;28mself\u001b[39m._trim_large_entries(params)\n\u001b[32m    403\u001b[39m LOGGER.debug(\u001b[33m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, command_info[\u001b[32m0\u001b[39m], url, \u001b[38;5;28mstr\u001b[39m(trimmed))\n\u001b[32m--> \u001b[39m\u001b[32m404\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand_info\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py:428\u001b[39m, in \u001b[36mRemoteConnection._request\u001b[39m\u001b[34m(self, method, url, body)\u001b[39m\n\u001b[32m    425\u001b[39m     body = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    427\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._client_config.keep_alive:\n\u001b[32m--> \u001b[39m\u001b[32m428\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_conn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_client_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    429\u001b[39m     statuscode = response.status\n\u001b[32m    430\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\urllib3\\_request_methods.py:135\u001b[39m, in \u001b[36mRequestMethods.request\u001b[39m\u001b[34m(self, method, url, body, fields, headers, json, **urlopen_kw)\u001b[39m\n\u001b[32m    132\u001b[39m     urlopen_kw[\u001b[33m\"\u001b[39m\u001b[33mbody\u001b[39m\u001b[33m\"\u001b[39m] = body\n\u001b[32m    134\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m method \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._encode_url_methods:\n\u001b[32m--> \u001b[39m\u001b[32m135\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest_encode_url\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    136\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    137\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    138\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfields\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfields\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[32m    139\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    140\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43murlopen_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    141\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    142\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    143\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.request_encode_body(\n\u001b[32m    144\u001b[39m         method, url, fields=fields, headers=headers, **urlopen_kw\n\u001b[32m    145\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\urllib3\\_request_methods.py:182\u001b[39m, in \u001b[36mRequestMethods.request_encode_url\u001b[39m\u001b[34m(self, method, url, fields, headers, **urlopen_kw)\u001b[39m\n\u001b[32m    179\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m fields:\n\u001b[32m    180\u001b[39m     url += \u001b[33m\"\u001b[39m\u001b[33m?\u001b[39m\u001b[33m\"\u001b[39m + urlencode(fields)\n\u001b[32m--> \u001b[39m\u001b[32m182\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mextra_kw\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\urllib3\\poolmanager.py:443\u001b[39m, in \u001b[36mPoolManager.urlopen\u001b[39m\u001b[34m(self, method, url, redirect, **kw)\u001b[39m\n\u001b[32m    441\u001b[39m     response = conn.urlopen(method, url, **kw)\n\u001b[32m    442\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m443\u001b[39m     response = \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest_uri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    445\u001b[39m redirect_location = redirect \u001b[38;5;129;01mand\u001b[39;00m response.get_redirect_location()\n\u001b[32m    446\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m redirect_location:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\urllib3\\connectionpool.py:787\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    784\u001b[39m response_conn = conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    786\u001b[39m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m787\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    788\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    789\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    790\u001b[39m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    791\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    792\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    793\u001b[39m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    794\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    797\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    799\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    800\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    802\u001b[39m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n\u001b[32m    803\u001b[39m clean_exit = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\urllib3\\connectionpool.py:534\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    532\u001b[39m \u001b[38;5;66;03m# Receive the response from the server\u001b[39;00m\n\u001b[32m    533\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m534\u001b[39m     response = \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    535\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (BaseSSLError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    536\u001b[39m     \u001b[38;5;28mself\u001b[39m._raise_timeout(err=e, url=url, timeout_value=read_timeout)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\site-packages\\urllib3\\connection.py:516\u001b[39m, in \u001b[36mHTTPConnection.getresponse\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    513\u001b[39m _shutdown = \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m.sock, \u001b[33m\"\u001b[39m\u001b[33mshutdown\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    515\u001b[39m \u001b[38;5;66;03m# Get the response from http.client.HTTPConnection\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m516\u001b[39m httplib_response = \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    518\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    519\u001b[39m     assert_header_parsing(httplib_response.msg)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\http\\client.py:1430\u001b[39m, in \u001b[36mHTTPConnection.getresponse\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1428\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m   1429\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1430\u001b[39m         \u001b[43mresponse\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1431\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n\u001b[32m   1432\u001b[39m         \u001b[38;5;28mself\u001b[39m.close()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\http\\client.py:331\u001b[39m, in \u001b[36mHTTPResponse.begin\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    329\u001b[39m \u001b[38;5;66;03m# read until we get a non-100 response\u001b[39;00m\n\u001b[32m    330\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m331\u001b[39m     version, status, reason = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    332\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m status != CONTINUE:\n\u001b[32m    333\u001b[39m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\http\\client.py:292\u001b[39m, in \u001b[36mHTTPResponse._read_status\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    291\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_read_status\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m292\u001b[39m     line = \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreadline\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_MAXLINE\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m, \u001b[33m\"\u001b[39m\u001b[33miso-8859-1\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    293\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(line) > _MAXLINE:\n\u001b[32m    294\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m LineTooLong(\u001b[33m\"\u001b[39m\u001b[33mstatus line\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\lang_env\\Lib\\socket.py:720\u001b[39m, in \u001b[36mSocketIO.readinto\u001b[39m\u001b[34m(self, b)\u001b[39m\n\u001b[32m    718\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m    719\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m720\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_sock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    721\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[32m    722\u001b[39m         \u001b[38;5;28mself\u001b[39m._timeout_occurred = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "import requests\n", "from bs4 import BeautifulSoup\n", "import time\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "# 5. LLM 모델 준비\n", "model = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "\n", "# 6. Prompt Template (중괄호 이스케이프)\n", "prompt_template = ChatPromptTemplate.from_template(dedent(\"\"\"\n", "다음은 상품 상세 페이지의 HTML 일부 소스입니다.\n", "\n", "이 HTML에는 상품에 대한 다양한 정보가 포함되어 있습니다.  \n", "다음 조건에 맞게 JSON 형식으로 변환해주세요:\n", "\n", "1. 상품과 관련된 정보는 가능한 한 모두 담아주세요.  \n", "2. HTML 내 존재하는 정보는 최대한 구체적으로 추출해 주세요.  \n", "3. 아래와 같은 JSON 구조를 따르세요. 단, 절대로, '```json', 'json', ' \\\"\\\"\\\" ', '**설명**' 등과 같이 불필요한 값은 채워넣지 않습니다.\n", "4. 만약 특정 항목이 HTML에 없다면 해당 항목은 생략하지 말고 빈 문자열(\"\")로 채워 주세요.  \n", "5. id 항목은 \"{product_id}\" 로 채워주세요. 쌍따옴표도 포함입니다. id, name, category, description 항목은 필수로 채워야합니다.\n", "6. specifications과 features 항목은 표, 리스트, 상세 스펙 등이 있을 경우나 제품에 대한 서술이 있을 경우 포함해주세요.\n", "7. 배송, 카드사 혜택, 포인트 적립 등의 공통 정보는 제외하고 상품에 직접적으로 관련된 정보만 포함하세요.\n", "8. features 항목은 제품의 특징을 포함합니다. 예를 들어 \"깔끔한 디자인\", \"고급스러운 소재\", \"가벼운 코튼 소재\", \"봄부터 여름까지 착용 가능한 제품\" 등과 같습니다.\n", "9. category 항목은 상품의 카테고리 또는 분류명을 포함합니다. 예를 들어 \"여성 의류 가디건\", \"남성 하의 팬츠\", \"액세서리 귀걸이\" 등입니다.\n", "10. description 항목은 상세 설명을 포함합니다. 길어도 좋으며, 제품의 특징을 최대한 포함하고자 합니다.\n", "11. 단, 전체 내용은 20만 토큰 이내로 유지해야 합니다.\n", "\n", "JSON 출력 예시 형태:\n", "\n", "{{\n", "  \"id\": {product_id}\n", "  \"name\": \"상품명\",\n", "  \"category\": \"카테고리 또는 분류명\",\n", "  \"description\": \"상세 설명. 길어도 좋음.\",\n", "  \"price\": \"가격 정보 (할인 전/후 가격 포함 가능)\",\n", "  \"specifications\": [\n", "    \"스펙 1\",\n", "    \"스펙 2\",\n", "    ...\n", "  ],\n", "  \"features\": [\n", "    \"특징 1\",\n", "    \"특징 2\",\n", "    ...\n", "  ],\n", "  \"keywords\": [\n", "    \"키워드 1\",\n", "    \"키워드 2\",\n", "    ...\n", "  ]\n", "}}\n", "\n", "다음은 HTML 소스입니다:\n", "{input_data}\n", "\n", "\"\"\"))\n", "\n", "# 7. 각 상품별로 들어가서 html 가져오기\n", "for idx, url in enumerate(urls[2896:], start=1):\n", "    e = time.time()\n", "    try:\n", "        driver = webdriver.Chrome()\n", "        driver.get(url)\n", "        time.sleep(2) \n", "\n", "        soup = BeautifulSoup(driver.page_source, 'lxml')\n", "        \n", "        target_1 = soup.select_one(\"#content > section > div.gods-summary > div.godsInfo-area\")\n", "        target_2 = soup.select_one(\"#godsTabView > div.gods-detail-desc.sticky-start > div > div\")\n", "\n", "        # 둘 중 없는 경우 대비\n", "        target_1_text = str(target_1) if target_1 else \"\"\n", "        target_2_text = str(target_2) if target_2 else \"\"\n", "\n", "        # 두 영역 합치기\n", "        extracted_html = target_1_text + \"\\n\\n\" + target_2_text\n", "\n", "        # 체인 실행\n", "        product_id = uuid4()\n", "        chain = prompt_template | model\n", "        result = chain.invoke({\"input_data\": extracted_html, \"product_id\": product_id})\n", "\n", "        # 결과 출력\n", "        # print(result.content)\n", "\n", "        # 파일에 저장\n", "        import json\n", "        import os\n", "\n", "        new_data_text = result.content  # LLM 응답 JSON 문자열\n", "\n", "        try:\n", "            new_data = json.loads(new_data_text)\n", "        except json.JSONDecodeError as error:\n", "            print(\"JSON 파싱 에러:\", error)\n", "            print(\"new_data_text : \", new_data_text)\n", "            new_data = None\n", "            continue\n", "\n", "        if new_data is None:\n", "            print(\"유효한 JSON 데이터가 아니므로 종료합니다.\")\n", "        else:\n", "            filename = '../data/raw_docs/product_info.json'\n", "\n", "        # 기존 데이터 불러오기 (파일 없으면 빈 리스트로 시작)\n", "        if os.path.exists(filename):\n", "            with open(filename, 'r', encoding='utf-8') as f:\n", "                try:\n", "                    existing_data = json.load(f)\n", "                except json.JSONDecodeError:\n", "                    existing_data = []\n", "        else:\n", "            existing_data = []\n", "\n", "        # existing_data가 리스트인지 확인. 아니면 리스트로 감싸기\n", "        if not isinstance(existing_data, list):\n", "            existing_data = [existing_data]\n", "\n", "        # 같은 name 있는지 검사\n", "        exists = any(item.get('name') == new_data.get('name') for item in existing_data)\n", "\n", "        if exists:\n", "            print(f\"'{new_data.get('name')}' 상품이 이미 존재합니다. 추가하지 않습니다.\")\n", "        else:\n", "            existing_data.append(new_data)\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                json.dump(existing_data, f, ensure_ascii=False, indent=2)\n", "            print(f\"'{new_data.get('name')}' 상품 정보를 추가 저장했습니다.\")\n", "\n", "    except Exception as error:\n", "        print(f\"[{idx}] 에러 발생:\", error)\n", "        continue\n", "    finally:\n", "        s = time.time()\n", "        print(f\"{idx}번 째 상품 로딩에 {s-e} 초 걸렸습니다\")\n", "        driver.quit()\n"]}, {"cell_type": "code", "execution_count": 32, "id": "37b12cec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3796 개의 제품이 들어있습니당!\n"]}], "source": ["from langchain_text_splitters import RecursiveJsonSplitter, RecursiveCharacterTextSplitter\n", "from langchain_core.documents import Document\n", "import json\n", "\n", "with open(\"../data/raw_docs/product_info.json\", 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "    print(f\"{len(data)} 개의 제품이 들어있습니당!\")"]}, {"cell_type": "code", "execution_count": null, "id": "27b8c1ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["제품 문서 3796개 처리 완료\n", "0번 째 batch 추가 완료@\n", "100번 째 batch 추가 완료@\n", "200번 째 batch 추가 완료@\n", "300번 째 batch 추가 완료@\n", "400번 째 batch 추가 완료@\n", "500번 째 batch 추가 완료@\n", "600번 째 batch 추가 완료@\n", "700번 째 batch 추가 완료@\n", "800번 째 batch 추가 완료@\n", "900번 째 batch 추가 완료@\n", "1000번 째 batch 추가 완료@\n", "1100번 째 batch 추가 완료@\n", "1200번 째 batch 추가 완료@\n", "1300번 째 batch 추가 완료@\n", "1400번 째 batch 추가 완료@\n", "1500번 째 batch 추가 완료@\n", "1600번 째 batch 추가 완료@\n", "1700번 째 batch 추가 완료@\n", "1800번 째 batch 추가 완료@\n", "1900번 째 batch 추가 완료@\n", "2000번 째 batch 추가 완료@\n", "2100번 째 batch 추가 완료@\n", "2200번 째 batch 추가 완료@\n", "2300번 째 batch 추가 완료@\n", "2400번 째 batch 추가 완료@\n", "2500번 째 batch 추가 완료@\n", "2600번 째 batch 추가 완료@\n", "2700번 째 batch 추가 완료@\n", "2800번 째 batch 추가 완료@\n", "2900번 째 batch 추가 완료@\n", "3000번 째 batch 추가 완료@\n", "3100번 째 batch 추가 완료@\n", "3200번 째 batch 추가 완료@\n", "3300번 째 batch 추가 완료@\n", "3400번 째 batch 추가 완료@\n", "3500번 째 batch 추가 완료@\n", "3600번 째 batch 추가 완료@\n", "3700번 째 batch 추가 완료@\n"]}], "source": ["import os\n", "from langchain.vectorstores import Chroma\n", "from langchain.embeddings import OpenAIEmbeddings\n", "from dotenv import load_dotenv\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "import json\n", "from langchain_core.documents import Document\n", "\n", "# .env 파일에서 OpenAI API 키 불러오기\n", "load_dotenv()\n", "\n", "# 임베딩 모델 초기화\n", "embedding_model = OpenAIEmbeddings()\n", "product_file = \"../data/raw_docs/product_info.json\"\n", "documents = []\n", "\n", "try:\n", "    with open(product_file, 'r', encoding='utf-8') as f:\n", "        product_data = json.load(f)\n", "    \n", "    for product in product_data:\n", "        # 사양 정보 포맷팅\n", "        specs = product.get('specifications', {})\n", "        # specs_text = \", \".join([f\"{k}: {v}\" for k, v in specs.items()]) if isinstance(specs, dict) else str(specs)\n", "        specs_text = \"\\n\".join(specs) if isinstance(specs, list) else str(specs)\n", "        \n", "        # 특징 정보 포맷팅\n", "        features = product.get('features', [])\n", "        # features_text = \", \".join(features) if isinstance(features, list) else str(features)\n", "        features_text = \"\\n\".join(features) if isinstance(features, list) else str(features)\n", "        \n", "        doc = Document(\n", "            page_content=f\"상품명: {product['name']}\\n카테고리: {product['category']}\\n키워드: {product['keywords']}\\n설명: {product['description']}\\n특징: {features_text}\\n가격: {product['price']}원\",\n", "            metadata={\n", "                \"source\": \"product\",\n", "                \"product_id\": product.get('product_id', ''),\n", "                \"price\": product.get('price', 0),\n", "                \"category\": product.get('category', '기타'),\n", "                \"keywords\": str(product.get('keywords', ''))\n", "            }\n", "        )\n", "        documents.append(doc)\n", "\n", "    print(f\"제품 문서 {len(documents)}개 처리 완료\")\n", "    \n", "except Exception as e:\n", "    print(f\"제품 문서 처리 실패: {e}\")\n", "\n", "# Splitter 초기화\n", "splitter = RecursiveCharacterTextSplitter(\n", "chunk_size=1000,\n", "chunk_overlap=200,\n", "# separators=[\"\\n\\n\", \"\\n\", \".\", \"!\", \"?\", \",\", \" \", \"\"]\n", ")\n", "# res = splitter.split_documents(documents[:1])\n", "# len(res)\n", "\n", "vectorstore = Chroma(\n", "    embedding_function=embedding_model,\n", "    persist_directory=str('vector_store_test')\n", ")\n", "BATCH_SIZE = 500\n", "for i in range(0, len(documents), BATCH_SIZE):\n", "    vectorstore.add_documents(documents[i:i+BATCH_SIZE])\n", "    print(f\"{i}번 째 batch 추가 완료@\")"]}, {"cell_type": "code", "execution_count": null, "id": "d64cccb8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lang_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}