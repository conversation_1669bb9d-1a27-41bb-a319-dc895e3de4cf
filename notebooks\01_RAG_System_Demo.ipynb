{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# RAG 기반 쇼핑몰 챗봇 시스템 데모\n", "\n", "이 노트북에서는 RAG 기반 쇼핑몰 고객 서비스 챗봇의 주요 기능들을 테스트하고 시연합니다.\n", "\n", "## 주요 구성 요소\n", "1. **Intent Classifier**: 사용자 의도 분류\n", "2. **RAG Processor**: FAQ 및 상품 정보 검색\n", "3. **DB Query Engine**: 주문/사용자 정보 조회\n", "4. **Delivery API Wrapper**: 배송 추적\n", "5. **Response Styler**: 응답 스타일링"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 9, 'prompt_tokens': 11, 'total_tokens': 20, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_34a54ae93c', 'id': 'chatcmpl-BmF8bcPY6yRGdmskDLfycA4NPYw9O', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--87ff9d05-d103-4ddb-a208-196a0362a6e0-0', usage_metadata={'input_tokens': 11, 'output_tokens': 9, 'total_tokens': 20, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import ChatOpenAI\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "llm.invoke(\"Hello, world!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 필요한 라이브러리 임포트\n", "import sys\n", "from pathlib import Path\n", "\n", "# 프로젝트 루트를 Python 경로에 추가\n", "project_root = Path().absolute().parent\n", "sys.path.append(str(project_root))\n", "\n", "print(f\"프로젝트 루트: {project_root}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 핵심 모듈들 임포트\n", "from core.intent_classifier import IntentClassifier, IntentType\n", "from core.rag_processor import RAGProcessor\n", "from core.db_query_engine import DatabaseQueryEngine\n", "from core.delivery_api_wrapper import DeliveryAPIWrapper\n", "from core.response_styler import ResponseStyler, ResponseTone\n", "\n", "print(\"✅ 모든 모듈 임포트 완료\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 의도 분류기 테스트"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 의도 분류기 초기화\n", "intent_classifier = IntentClassifier()\n", "\n", "# 테스트 질문들\n", "test_questions = [\n", "    \"배송비는 얼마인가요?\",\n", "    \"무선 이어폰 사양이 어떻게 되나요?\",\n", "    \"내 주문 ORD20241201001 상태 알려주세요\",\n", "    \"운송장번호 123456789012 배송 현황 확인해주세요\",\n", "    \"반품은 어떻게 하나요?\",\n", "    \"안녕하세요\",\n", "    \"이상한 질문입니다\"\n", "]\n", "\n", "print(\"🔍 의도 분류 테스트 결과:\")\n", "print(\"=\" * 50)\n", "\n", "for question in test_questions:\n", "    result = intent_classifier.classify(question)\n", "    strategy = intent_classifier.get_processing_strategy(result)\n", "    \n", "    print(f\"질문: {question}\")\n", "    print(f\"의도: {result.intent.value} (신뢰도: {result.confidence:.2f})\")\n", "    print(f\"엔티티: {result.entities}\")\n", "    print(f\"처리 전략: {strategy}\")\n", "    print(\"-\" * 30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. RAG 프로세서 테스트"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RAG 프로세서 초기화\n", "rag_processor = RAGProcessor()\n", "rag_processor.initialize_vector_store()\n", "\n", "print(\"✅ RAG 프로세서 초기화 완료\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FAQ 및 상품 정보 검색 테스트\n", "rag_test_queries = [\n", "    \"배송비는 얼마인가요?\",\n", "    \"무선 이어폰 배터리 지속시간은?\",\n", "    \"반품 절차를 알려주세요\",\n", "    \"스마트 워치 방수 기능이 있나요?\"\n", "]\n", "\n", "print(\"📚 RAG 검색 테스트 결과:\")\n", "print(\"=\" * 50)\n", "\n", "for query in rag_test_queries:\n", "    result = rag_processor.process_query(query)\n", "    \n", "    print(f\"질문: {query}\")\n", "    print(f\"답변: {result['response'][:200]}...\")\n", "    print(f\"신뢰도: {result['confidence']:.2f}\")\n", "    print(f\"소스 수: {len(result['sources'])}\")\n", "    \n", "    if result['sources']:\n", "        print(\"소스 정보:\")\n", "        for i, source in enumerate(result['sources'][:2], 1):\n", "            print(f\"  {i}. {source['source']} - {source['content_preview'][:50]}...\")\n", "    \n", "    print(\"-\" * 30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 데이터베이스 쿼리 엔진 테스트"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DB 쿼리 엔진 초기화\n", "db_engine = DatabaseQueryEngine()\n", "\n", "print(\"🗄️ 데이터베이스 쿼리 테스트:\")\n", "print(\"=\" * 50)\n", "\n", "# 주문 조회 테스트\n", "print(\"1. 주문 조회 테스트:\")\n", "order = db_engine.get_order_by_id(\"ORD20241201001\")\n", "if order:\n", "    print(\"✅ 주문 조회 성공\")\n", "    print(db_engine.format_order_info(order))\n", "else:\n", "    print(\"❌ 주문 조회 실패 - 데이터베이스를 초기화해주세요\")\n", "\n", "print(\"\\n2. 사용자 주문 목록 테스트:\")\n", "orders = db_engine.get_recent_orders_by_phone(\"010-1234-5678\")\n", "if orders:\n", "    print(\"✅ 사용자 주문 목록 조회 성공\")\n", "    print(db_engine.format_user_orders(orders))\n", "else:\n", "    print(\"❌ 사용자 주문 목록 조회 실패\")\n", "\n", "print(\"\\n3. 상품 검색 테스트:\")\n", "products = db_engine.search_products(\"이어폰\")\n", "if products:\n", "    print(\"✅ 상품 검색 성공\")\n", "    for product in products[:3]:\n", "        print(f\"- {product['name']} ({product['price']:,}원)\")\n", "else:\n", "    print(\"❌ 상품 검색 실패\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 배송 API 래퍼 테스트"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 배송 API 래퍼 초기화\n", "delivery_api = DeliveryAPIWrapper()\n", "\n", "print(\"🚚 배송 API 테스트:\")\n", "print(\"=\" * 50)\n", "\n", "# 배송 추적 테스트\n", "print(\"1. 배송 추적 테스트:\")\n", "tracking_info = delivery_api.track_package(\"123456789012\")\n", "if tracking_info:\n", "    print(\"✅ 배송 추적 성공\")\n", "    print(delivery_api.format_delivery_info(tracking_info))\n", "else:\n", "    print(\"❌ 배송 추적 실패\")\n", "\n", "print(\"\\n2. 배송 예상 시간 테스트:\")\n", "estimate = delivery_api.get_delivery_estimate(\"서울\", \"부산\")\n", "print(f\"✅ 배송 예상: {estimate['description']}\")\n", "\n", "print(\"\\n3. 배송 가능 지역 테스트:\")\n", "availability = delivery_api.check_delivery_availability(\"제주시 연동\")\n", "print(f\"✅ 배송 가능: {availability['available']}\")\n", "if availability.get('note'):\n", "    print(f\"참고사항: {availability['note']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 응답 스타일러 테스트"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 응답 스타일러 초기화\n", "response_styler = ResponseStyler()\n", "\n", "print(\"✨ 응답 스타일러 테스트:\")\n", "print(\"=\" * 50)\n", "\n", "# 다양한 톤으로 스타일링 테스트\n", "test_responses = [\n", "    (\"배송비는 5만원 이상 주문시 무료입니다.\", ResponseTone.FRIENDLY),\n", "    (\"주문번호 ORD20241201001의 상태는 배송중입니다.\", ResponseTone.INFORMATIVE),\n", "    (\"죄송합니다. 해당 정보를 찾을 수 없습니다.\", ResponseTone.APOLOGETIC),\n", "    (\"상품 사양은 다음과 같습니다.\", ResponseTone.HELPFUL)\n", "]\n", "\n", "for i, (response, tone) in enumerate(test_responses, 1):\n", "    styled = response_styler.style_response(response, tone, include_greeting=True)\n", "    \n", "    print(f\"{i}. {tone.value.upper()} 톤:\")\n", "    print(f\"원본: {response}\")\n", "    print(f\"스타일링: {styled}\")\n", "    print(\"-\" * 30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 통합 챗봇 시뮬레이션"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 통합 챗봇 시뮬레이션 함수\n", "def simulate_chatbot_conversation(user_input):\n", "    \"\"\"챗봇 대화 시뮬레이션\"\"\"\n", "    print(f\"👤 사용자: {user_input}\")\n", "    \n", "    # 1. 의도 분류\n", "    intent_result = intent_classifier.classify(user_input)\n", "    strategy = intent_classifier.get_processing_strategy(intent_result)\n", "    \n", "    print(f\"🧠 의도: {intent_result.intent.value} (신뢰도: {intent_result.confidence:.2f})\")\n", "    print(f\"⚙️ 처리 전략: {strategy}\")\n", "    \n", "    # 2. 전략에 따른 응답 생성\n", "    if strategy == \"rag_processor\":\n", "        result = rag_processor.process_query(user_input)\n", "        response = result['response']\n", "    elif strategy == \"db_query\":\n", "        entities = intent_result.entities\n", "        if 'order_id' in entities:\n", "            order = db_engine.get_order_by_id(entities['order_id'])\n", "            response = db_engine.format_order_info(order) if order else \"주문을 찾을 수 없습니다.\"\n", "        else:\n", "            response = \"주문 조회를 위해 주문번호를 알려주세요.\"\n", "    elif strategy == \"delivery_api\":\n", "        entities = intent_result.entities\n", "        if 'tracking_number' in entities:\n", "            delivery_info = delivery_api.track_package(entities['tracking_number'])\n", "            response = delivery_api.format_delivery_info(delivery_info) if delivery_info else \"배송 정보를 찾을 수 없습니다.\"\n", "        else:\n", "            response = \"배송 조회를 위해 운송장번호를 알려주세요.\"\n", "    else:\n", "        response = \"안녕하세요! 무엇을 도와드릴까요?\"\n", "    \n", "    # 3. 응답 스타일링\n", "    tone = response_styler.determine_tone(intent_result.intent.value, intent_result.confidence)\n", "    styled_response = response_styler.style_response(response, tone, include_greeting=True)\n", "    \n", "    print(f\"🤖 챗봇: {styled_response}\")\n", "    print(\"=\" * 80)\n", "\n", "# 대화 시뮬레이션\n", "conversation_examples = [\n", "    \"안녕하세요!\",\n", "    \"배송비는 얼마인가요?\",\n", "    \"무선 이어폰 배터리 시간이 궁금해요\",\n", "    \"주문번호 ORD20241201001 상태 확인해주세요\",\n", "    \"운송장번호 123456789012 배송 현황 알려주세요\"\n", "]\n", "\n", "print(\"💬 챗봇 대화 시뮬레이션:\")\n", "print(\"=\" * 80)\n", "\n", "for example in conversation_examples:\n", "    simulate_chatbot_conversation(example)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 결론\n", "\n", "이 노트북에서는 RAG 기반 쇼핑몰 챗봇 시스템의 주요 구성 요소들을 테스트했습니다:\n", "\n", "1. ✅ **의도 분류기**: 사용자 질문의 의도를 정확히 분류\n", "2. ✅ **RAG 프로세서**: FAQ와 상품 정보를 효과적으로 검색\n", "3. ✅ **DB 쿼리 엔진**: 주문과 사용자 정보를 정확히 조회\n", "4. ✅ **배송 API**: 배송 추적 정보를 제공\n", "5. ✅ **응답 스타일러**: 일관되고 친근한 응답 생성\n", "\n", "모든 구성 요소가 정상적으로 작동하며, 통합된 챗봇 시스템이 다양한 고객 문의에 적절히 응답할 수 있음을 확인했습니다.\n", "\n", "### 다음 단계\n", "- Streamlit 앱 실행: `streamlit run app/main.py`\n", "- 추가 테스트 및 성능 최적화\n", "- 실제 운영 환경 배포 준비"]}], "metadata": {"kernelspec": {"display_name": "3rd", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}